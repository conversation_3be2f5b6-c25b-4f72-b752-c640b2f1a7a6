[2025-08-04 16:14:00.746] [info]  main 日志系统初始化完成 {
  path: 'C:\\myFile\\workspaces\\aliyun\\dev\\mattverse-vite\\logs\\main-2025-08-04.log',
  time: '2025-08-04T08:14:00.744Z'
}
[2025-08-04 16:14:00.750] [info]  IPC handler registered: app:get-version
[2025-08-04 16:14:00.751] [info]  IPC handler registered: app:get-info
[2025-08-04 16:14:00.752] [info]  IPC handler registered: app:quit
[2025-08-04 16:14:00.753] [info]  IPC handler registered: app:restart
[2025-08-04 16:14:00.754] [info]  IPC handler registered: window:minimize
[2025-08-04 16:14:00.754] [info]  IPC handler registered: window:maximize
[2025-08-04 16:14:00.755] [info]  IPC handler registered: window:close
[2025-08-04 16:14:00.756] [info]  IPC handler registered: window:get-state
[2025-08-04 16:14:00.757] [info]  IPC handler registered: window:set-size
[2025-08-04 16:14:00.758] [info]  IPC handler registered: window:center
[2025-08-04 16:14:00.759] [info]  IPC handler registered: dialog:select-file
[2025-08-04 16:14:00.760] [info]  IPC handler registered: dialog:select-folder
[2025-08-04 16:14:00.761] [info]  IPC handler registered: dialog:save-file
[2025-08-04 16:14:00.762] [info]  IPC handler registered: dialog:show-message
[2025-08-04 16:14:00.763] [info]  IPC handler registered: logger:info
[2025-08-04 16:14:00.764] [info]  IPC handler registered: logger:warn
[2025-08-04 16:14:00.765] [info]  IPC handler registered: logger:error
[2025-08-04 16:14:00.766] [info]  IPC handler registered: logger:debug
[2025-08-04 16:14:00.767] [info]  IPC handler registered: grpc:init
[2025-08-04 16:14:00.768] [info]  IPC handler registered: grpc:ping
[2025-08-04 16:14:00.769] [info]  IPC handler registered: grpc:call
[2025-08-04 16:14:00.770] [info]  IPC handler registered: grpc:get-status
[2025-08-04 16:14:00.770] [info]  IPC handler registered: middleware:get-config
[2025-08-04 16:14:00.772] [info]  IPC handler registered: middleware:update-config
[2025-08-04 16:14:00.773] [info]  IPC handler registered: middleware:get-version
[2025-08-04 16:14:00.774] [info]  IPC handler registered: middleware:test-connection
[2025-08-04 16:14:00.775] [info]  IPC handler registered: middleware:restart-app
[2025-08-04 16:14:00.776] [info]  IPC handler registered: mattverse:get-config
[2025-08-04 16:14:00.777] [info]  IPC handler registered: mattverse:get-workflows
[2025-08-04 16:14:00.778] [info]  IPC handler registered: mattverse:save-workflow
[2025-08-04 16:14:00.779] [info]  IPC handler registered: mattverse:delete-workflow
[2025-08-04 16:14:00.859] [info]  DevTools: 已安装 VUE_DEVTOOLS
[2025-08-04 16:14:00.861] [info]  DevTools: 扩展安装完成，共安装 1 个扩展
[2025-08-04 16:14:00.900] [info]  Mattverse window created!
[2025-08-04 16:14:00.902] [info]  Mattverse app is ready!
[2025-08-04 16:14:00.903] [info]  使用 middle.config.json 配置: {
  host: '***********',
  port: 29998,
  updatedAt: '2025-08-04T06:30:58.128Z',
  source: 'user_modified'
}
[2025-08-04 16:14:00.904] [info]  尝试连接 gRPC 服务器: ***********:29998
[2025-08-04 16:14:00.906] [info]  加载 proto 文件: C:\myFile\workspaces\aliyun\dev\mattverse-vite\apps\mattverse\out\main\protos\linker.proto
[2025-08-04 16:14:00.933] [warn]  连接未就绪，状态: 0
[2025-08-04 16:14:00.934] [info]  Mattverse gRPC 客户端初始化成功: {
  host: '***********',
  port: 29998,
  userId: '0',
  token: 'abcdefghijklmn'
}
[2025-08-04 16:14:00.935] [info]  Mattverse gRPC 客户端已在应用启动时初始化
[2025-08-04 16:14:00.936] [info]  Electron app started
[2025-08-04 16:14:07.038] [info]  DevTools: 自动打开开发者工具
[2025-08-04 16:14:07.964] [info]  Main window is ready to show
[2025-08-04 16:15:02.931] [info]  使用 middle.config.json 配置: {
  host: '***********',
  port: 29998,
  updatedAt: '2025-08-04T06:30:58.128Z',
  source: 'user_modified'
}
[2025-08-04 16:15:02.935] [info]  [Renderer] Loaded middleware config: { host: '***********', port: 29998 }
[2025-08-04 16:15:02.990] [info]  获取中台版本成功: {
  status: 'Success',
  message: '',
  result: '0.4.5+5',
  result_type: 'String'
}
[2025-08-04 16:15:02.993] [info]  [Renderer] Got middleware version: { version: '0.4.5+5' }
[2025-08-04 17:52:43.858] [info]  Main window is ready to show
[2025-08-04 20:05:24.539] [info]  main 日志系统初始化完成 {
  path: 'C:\\myFile\\workspaces\\aliyun\\dev\\mattverse-vite\\logs\\main-2025-08-04.log',
  time: '2025-08-04T12:05:24.538Z'
}
[2025-08-04 20:05:24.541] [info]  IPC handler registered: app:get-version
[2025-08-04 20:05:24.541] [info]  IPC handler registered: app:get-info
[2025-08-04 20:05:24.542] [info]  IPC handler registered: app:quit
[2025-08-04 20:05:24.542] [info]  IPC handler registered: app:restart
[2025-08-04 20:05:24.543] [info]  IPC handler registered: window:minimize
[2025-08-04 20:05:24.543] [info]  IPC handler registered: window:maximize
[2025-08-04 20:05:24.543] [info]  IPC handler registered: window:close
[2025-08-04 20:05:24.544] [info]  IPC handler registered: window:get-state
[2025-08-04 20:05:24.544] [info]  IPC handler registered: window:set-size
[2025-08-04 20:05:24.545] [info]  IPC handler registered: window:center
[2025-08-04 20:05:24.545] [info]  IPC handler registered: dialog:select-file
[2025-08-04 20:05:24.546] [info]  IPC handler registered: dialog:select-folder
[2025-08-04 20:05:24.546] [info]  IPC handler registered: dialog:save-file
[2025-08-04 20:05:24.546] [info]  IPC handler registered: dialog:show-message
[2025-08-04 20:05:24.547] [info]  IPC handler registered: logger:info
[2025-08-04 20:05:24.547] [info]  IPC handler registered: logger:warn
[2025-08-04 20:05:24.547] [info]  IPC handler registered: logger:error
[2025-08-04 20:05:24.548] [info]  IPC handler registered: logger:debug
[2025-08-04 20:05:24.548] [info]  IPC handler registered: grpc:init
[2025-08-04 20:05:24.548] [info]  IPC handler registered: grpc:ping
[2025-08-04 20:05:24.549] [info]  IPC handler registered: grpc:call
[2025-08-04 20:05:24.550] [info]  IPC handler registered: grpc:get-status
[2025-08-04 20:05:24.551] [info]  IPC handler registered: middleware:get-config
[2025-08-04 20:05:24.551] [info]  IPC handler registered: middleware:update-config
[2025-08-04 20:05:24.552] [info]  IPC handler registered: middleware:get-version
[2025-08-04 20:05:24.552] [info]  IPC handler registered: middleware:test-connection
[2025-08-04 20:05:24.553] [info]  IPC handler registered: middleware:restart-app
[2025-08-04 20:05:24.553] [info]  IPC handler registered: mattverse:get-config
[2025-08-04 20:05:24.553] [info]  IPC handler registered: mattverse:get-workflows
[2025-08-04 20:05:24.554] [info]  IPC handler registered: mattverse:save-workflow
[2025-08-04 20:05:24.554] [info]  IPC handler registered: mattverse:delete-workflow
[2025-08-04 20:05:24.622] [info]  DevTools: 已安装 VUE_DEVTOOLS
[2025-08-04 20:05:24.623] [info]  DevTools: 扩展安装完成，共安装 1 个扩展
[2025-08-04 20:05:24.664] [info]  Mattverse window created!
[2025-08-04 20:05:24.665] [info]  Mattverse app is ready!
[2025-08-04 20:05:24.668] [info]  使用 middle.config.json 配置: {
  host: '***********',
  port: 29998,
  updatedAt: '2025-08-04T06:30:58.128Z',
  source: 'user_modified'
}
[2025-08-04 20:05:24.669] [info]  尝试连接 gRPC 服务器: ***********:29998
[2025-08-04 20:05:24.670] [info]  加载 proto 文件: C:\myFile\workspaces\aliyun\dev\mattverse-vite\apps\mattverse\out\main\protos\linker.proto
[2025-08-04 20:05:24.699] [warn]  连接未就绪，状态: 0
[2025-08-04 20:05:24.700] [info]  Mattverse gRPC 客户端初始化成功: {
  host: '***********',
  port: 29998,
  userId: '0',
  token: 'abcdefghijklmn'
}
[2025-08-04 20:05:24.701] [info]  Mattverse gRPC 客户端已在应用启动时初始化
[2025-08-04 20:05:24.701] [info]  Electron app started
[2025-08-04 20:05:30.837] [info]  DevTools: 自动打开开发者工具
[2025-08-04 20:05:31.756] [info]  Main window is ready to show
[2025-08-04 20:07:09.869] [info]  使用 middle.config.json 配置: {
  host: '***********',
  port: 29998,
  updatedAt: '2025-08-04T06:30:58.128Z',
  source: 'user_modified'
}
[2025-08-04 20:07:09.888] [info]  [Renderer] Loaded middleware config: { host: '***********', port: 29998 }
[2025-08-04 20:07:09.954] [info]  获取中台版本成功: {
  status: 'Success',
  message: '',
  result: '0.4.5+5',
  result_type: 'String'
}
[2025-08-04 20:07:09.959] [info]  [Renderer] Got middleware version: { version: '0.4.5+5' }
[2025-08-04 20:07:16.996] [info]  [Renderer] App config loaded: {
  appName: 'Mattverse',
  version: '1.1.0',
  features: [ 'workflow', 'ai', 'automation' ]
}
[2025-08-04 20:07:16.998] [info]  [Renderer] System info loaded: {
  platform: 'Windows',
  arch: 'x64',
  nodeVersion: '20.18.3',
  electronVersion: '33.4.11',
  chromeVersion: '130.0.6723.191'
}
[2025-08-04 20:07:17.000] [info]  [Renderer] Available electronAPI properties: [
  'app:get-version',    'app:get-info',
  'app:quit',           'app:restart',
  'window:minimize',    'window:maximize',
  'window:close',       'window:get-state',
  'window:set-size',    'window:center',
  'dialog:select-file', 'dialog:select-folder',
  'dialog:save-file',   'dialog:show-message',
  'store:get',          'store:set',
  'store:delete',       'store:clear',
  'store:has',          'fs:read-file',
  'fs:write-file',      'fs:exists',
  'fs:mkdir',           'logger:info',
  'logger:warn',        'logger:error',
  'logger:debug',       'events',
  'platform',           'arch',
  'versions',           'getConfig',
  'mattverse',          'middleware',
  'grpc',               'invoke',
  'send',               'on',
  'once'
]
[2025-08-04 20:07:17.004] [info]  [Renderer] Platform info: win32
[2025-08-04 20:07:17.007] [info]  [Renderer] Arch info: x64
[2025-08-04 20:07:17.009] [info]  [Renderer] Versions info: { node: '20.18.3', electron: '33.4.11', chrome: '130.0.6723.191' }
[2025-08-04 20:07:19.771] [info]  使用 middle.config.json 配置: {
  host: '***********',
  port: 29998,
  updatedAt: '2025-08-04T06:30:58.128Z',
  source: 'user_modified'
}
[2025-08-04 20:07:19.781] [info]  [Renderer] Loaded middleware config: { host: '***********', port: 29998 }
[2025-08-04 20:07:19.832] [info]  获取中台版本成功: {
  status: 'Success',
  message: '',
  result: '0.4.5+5',
  result_type: 'String'
}
[2025-08-04 20:07:19.834] [info]  [Renderer] Got middleware version: { version: '0.4.5+5' }
